<?if $(sys.B<PERSON><PERSON>ARCH)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="RealityTap Haptics Studio"
            UpgradeCode="dfa82347-b28e-549b-8f38-eb7559a97d0a"
            Language="!(loc.TauriLanguage)"
            Manufacturer="AWA"
            Version="1.0.9">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="RealityTap Haptics Studio" Description="Runs RealityTap Haptics Studio" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="RealityTap Haptics Studio"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="RealityTap Haptics Studio"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="0147ef6a-fc30-5fd9-bd41-312c6ae15b1a" Win64="$(var.Win64)">
                <File Id="Path" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\realitytap_studio.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Component Id="I163a9149b4804f33a16fb897d0a9a5c0" Guid="903291e2-ed54-41e0-9713-ad9d7df28fc7" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I163a9149b4804f33a16fb897d0a9a5c0" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtcore.dll" /></Component><Component Id="If848c2a64511495dbb566216b50af363" Guid="45da0abd-3db5-41b7-951b-6f475696db23" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If848c2a64511495dbb566216b50af363" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtssl.dll" /></Component><Component Id="I000eb3c465b0461bb27319c268c98f40" Guid="8e0d3475-949a-400c-8279-02a7e7d9ffda" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I000eb3c465b0461bb27319c268c98f40" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\librtutils.dll" /></Component><Component Id="Id91add5c42414272ad748920d5216d05" Guid="943dfeed-c426-49d4-a11c-a4ead98b2159" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id91add5c42414272ad748920d5216d05" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libgcc_s_seh-1.dll" /></Component><Component Id="Ie430e111143f47df851ad7f97d6ff2bd" Guid="363111b9-1cc4-4189-a097-71bff879b129" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie430e111143f47df851ad7f97d6ff2bd" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libstdc++-6.dll" /></Component><Component Id="I4c483b9949334631bcbebe947131ec43" Guid="16e52578-7d3e-4a5f-8d47-1243955cea4e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4c483b9949334631bcbebe947131ec43" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\libwinpthread-1.dll" /></Component><Component Id="I43eccc3889b74d478217b6d387df081d" Guid="074df699-43c9-4899-883a-a80fe0f6998c" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I43eccc3889b74d478217b6d387df081d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffmpeg.exe" /></Component><Component Id="If82e4c975190483bbb39c719ef1e72e0" Guid="b2fe6fd3-d29c-4648-b6a3-5c8b03e23fa5" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If82e4c975190483bbb39c719ef1e72e0" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\ffprobe.exe" /></Component><Directory Id="I09d20e1551554556a2bc3ca2b72b9e27" Name="motors"><Component Id="I5f425f2b81db4c9ea21fca4547e736d4" Guid="2f494575-a685-4b6f-8a72-d8d4fa838052" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5f425f2b81db4c9ea21fca4547e736d4" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_normal_170Hz.conf" /></Component><Component Id="I00e212dc733a42249f08a7dea2f7f27d" Guid="6d630519-7329-46e5-8303-69cb2b835352" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I00e212dc733a42249f08a7dea2f7f27d" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0809_pro_170Hz.conf" /></Component><Component Id="Ib1bb50e1b2c443e385fe7d9ba213ab5a" Guid="35055fc6-5ffc-489f-9101-c217b8ec4919" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib1bb50e1b2c443e385fe7d9ba213ab5a" Source="E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\motors\LRA_0916_normal_170Hz.conf" /></Component></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall RealityTap Haptics Studio"
						  Description="Uninstalls RealityTap Haptics Studio"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\AWA\RealityTap Haptics Studio"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="RealityTap Haptics Studio"
                    Description="Runs RealityTap Haptics Studio"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.awa.realitytap.desktop"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\AWA\RealityTap Haptics Studio" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I163a9149b4804f33a16fb897d0a9a5c0"/>
<ComponentRef Id="If848c2a64511495dbb566216b50af363"/>
<ComponentRef Id="I000eb3c465b0461bb27319c268c98f40"/>
<ComponentRef Id="Id91add5c42414272ad748920d5216d05"/>
<ComponentRef Id="Ie430e111143f47df851ad7f97d6ff2bd"/>
<ComponentRef Id="I4c483b9949334631bcbebe947131ec43"/>
<ComponentRef Id="I43eccc3889b74d478217b6d387df081d"/>
<ComponentRef Id="If82e4c975190483bbb39c719ef1e72e0"/>
<ComponentRef Id="I5f425f2b81db4c9ea21fca4547e736d4"/>
<ComponentRef Id="I00e212dc733a42249f08a7dea2f7f27d"/>
<ComponentRef Id="Ib1bb50e1b2c443e385fe7d9ba213ab5a"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
